flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 226ms
flutter: ℹ️ INFO [2025-07-21T04:18:48.265625] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-07-21T04:18:48.274584] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-07-21T04:18:48.275871] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-07-21T04:18:48.304393] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-07-21T04:18:48.334051] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-07-21T04:18:48.368281] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-07-21T04:18:48.379196] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-07-21T04:18:48.357376"}}
flutter: ℹ️ INFO [2025-07-21T04:18:48.387198] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-07-21T04:18:48.388013] [App] All services initialized successfully
flutter: SplashVideoBackground: Video failed to load, using fallback
flutter: VideoBackground: Failed to load video - PlatformException(video_player, *** -[NSURL initFileURLWithPath:]: nil string parameter, null, null)
flutter: 🐛 DEBUG [2025-07-21T04:18:51.712346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":214}
flutter: 🐛 DEBUG [2025-07-21T04:18:51.808707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":96}
flutter: 🐛 DEBUG [2025-07-21T04:18:51.897225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":87}
flutter: 🐛 DEBUG [2025-07-21T04:18:51.948682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:51.998818] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.113131] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.146480] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.181017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.246474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.281924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.316043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.351392] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.488982] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.514157] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.563366] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.623257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.700233] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.747293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.814329] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.883841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.930410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:52.963538] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.030592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.064373] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.130924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.163939] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.230607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.265693] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.303076] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.329948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.380613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-21T04:18:53.400795] [PerformanceMonitoringService] High memory usage detected {"memory_mb":198.0}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.449787] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.479895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.546994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.613028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.667548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.713063] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.746612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.780230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.831282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.863457] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.929904] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:53.979781] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.046656] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.098180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.130830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.179960] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.213347] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.254079] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.282653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.316468] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.350108] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.380544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.430341] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.463614] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.497236] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.546569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.579776] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.613412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.650515] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.681968] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.714721] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.747017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.813149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.863736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.899022] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.931193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:54.964498] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.029308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.047496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.082972] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.179801] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.213218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.247446] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.281031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.312992] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.368888] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.397155] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.431310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.464895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.613727] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.663488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.696845] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.730819] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.763177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.796393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.830401] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.863146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.896370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.930594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:55.963365] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.014592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.047241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.080055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.113361] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.147344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.196393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.229717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.263470] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.296486] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.347186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.380118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.429765] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.463303] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.619118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.679856] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.746569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.779963] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.814666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.846864] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.880392] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.930168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.965097] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:56.996930] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.049566] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.080791] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.146505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.196777] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.229809] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.264187] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.313748] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.346382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.379720] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.413531] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.448002] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.480488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.513476] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.547610] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.596382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.629745] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.663070] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.698676] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.729744] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.763692] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.814068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.847299] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.879845] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.916028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.947150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:57.980731] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.013749] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.047459] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.079770] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.113252] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.147298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.213678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.246549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.346522] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.430450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ⚠️ WARNING [2025-07-21T04:18:58.605529] [PerformanceMonitoringService] High memory usage detected {"memory_mb":155.0}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.606948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":177}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.646370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.662915] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.713485] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.747101] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.780246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.829822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.863205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.897162] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:58.929963] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.031415] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.064298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.131571] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.164065] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.215162] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.248667] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.280882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.313688] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.346979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.414179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.447697] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.479753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.514924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.546217] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.580615] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.614692] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.664417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.697784] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.747235] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.780393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.831025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.864182] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.913991] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.947127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:18:59.980581] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:00.064050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:00.096779] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:00.129856] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:00.215245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-07-21T04:19:00.907289] [PerformanceMonitoringService] Slow frame detected {"duration_ms":682}
flutter: 🐛 DEBUG [2025-07-21T04:19:01.267700] [PerformanceMonitoringService] Slow frame detected {"duration_ms":365}
flutter: 🐛 DEBUG [2025-07-21T04:19:01.306019] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-21T04:19:01.329879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-21T04:19:03.679959] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.131316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":218}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.246394] [PerformanceMonitoringService] Slow frame detected {"duration_ms":114}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.380194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.423818] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.563513] [PerformanceMonitoringService] Slow frame detected {"duration_ms":138}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.663523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.713532] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.747224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:08.863827] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:09.880011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:09.914239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:10.830038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":900}
flutter: 🐛 DEBUG [2025-07-21T04:19:11.131108] [PerformanceMonitoringService] Slow frame detected {"duration_ms":302}
flutter: 🐛 DEBUG [2025-07-21T04:19:11.180551] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:11.213125] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:11.324967] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-21T04:19:11.329889] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-07-21T04:19:13.315587] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-21T04:19:13.796246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:13.982613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":151}
flutter: 🐛 DEBUG [2025-07-21T04:19:14.245912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":265}
flutter: 🐛 DEBUG [2025-07-21T04:19:14.329967] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:14.379854] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:14.544406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":165}
flutter: 🐛 DEBUG [2025-07-21T04:19:14.563207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.485116] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.536866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.645268] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.705286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.730038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.865393] [PerformanceMonitoringService] Slow frame detected {"duration_ms":136}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.913510] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:16.980091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.096572] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.273378] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.292255] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.331780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.462818] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.512840] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.563902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:17.929786] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.060057] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.087715] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.112492] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.166473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.265064] [PerformanceMonitoringService] Slow frame detected {"duration_ms":102}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.313033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.363288] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.430116] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.846672] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.896879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:18.947977] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.033756] [PerformanceMonitoringService] Slow frame detected {"duration_ms":87}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.063100] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.112983] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.186108] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.201099] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.229842] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:19.579637] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.146188] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.212759] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.281820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.363280] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.623455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.646221] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.760648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":114}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.779408] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.830696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.863406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:20.982611] [PerformanceMonitoringService] Slow frame detected {"duration_ms":103}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.029899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.065197] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.196753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.279470] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.330642] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.362944] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.510643] [PerformanceMonitoringService] Slow frame detected {"duration_ms":147}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.529500] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.596006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.787150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":105}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.815318] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.847402] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:21.913964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.095990] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.146431] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.285259] [PerformanceMonitoringService] Slow frame detected {"duration_ms":139}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.314015] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.346473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.396180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.505812] [PerformanceMonitoringService] Slow frame detected {"duration_ms":109}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.529675] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.562727] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.596027] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.751895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":156}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.780218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:19:22.879853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.168226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":155}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.262195] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.297138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.338287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: ⚠️ WARNING [2025-07-21T04:19:23.402101] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.417278] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.462961] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.663596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.700869] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.729055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.779266] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.845680] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.888433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.917600] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-21T04:19:23.947032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.212949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.645921] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.713264] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.746978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.780218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:24.962609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.013416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.046085] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.133878] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.163485] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.201854] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.232304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.279320] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.486732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.513734] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.550711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.597450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.691219] [PerformanceMonitoringService] Slow frame detected {"duration_ms":95}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.720403] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.812256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.879805] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:25.913018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.404751] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.430878] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.463017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.513601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.567298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.596798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.629062] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.707441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.729015] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.763470] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:26.830672] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:27.472390] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-21T04:19:27.495961] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-21T04:19:27.529528] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:27.563224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.483154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.512540] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.598015] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.628985] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.712573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.763618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.813106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.863304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.912186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.946575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:30.996779] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:31.030095] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:31.062592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:31.146364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.729490] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.795269] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.846351] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.879253] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.929807] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:32.996543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.047370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.095245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.130381] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.164417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.195681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.896558] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.942245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-21T04:19:33.979194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.029672] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.112923] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.179678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.245311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.280030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.346194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.380017] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:34.530250] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:35.946830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:35.996338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:36.045653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:36.745522] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:36.779148] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:36.812250] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:36.862800] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-21T04:19:38.383667] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.262823] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.332417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.362610] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.412991] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.462711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.529261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.562779] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.612886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.646310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:39.679881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:40.745766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-21T04:19:40.884256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":122}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-21T04:19:40.982106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.029520] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.079733] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.129435] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.162857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.196314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.264403] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.271645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.295174] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.331333] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.396666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.430048] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.479598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.513313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.562266] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.596210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.646508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.678379] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.713206] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.746905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.779815] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.828862] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.862841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.930151] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.962453] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:41.996735] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:42.029042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: 🐛 DEBUG [2025-07-21T04:19:42.086576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: AutoLockService initialized with settings
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-21T04:19:43.110843] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-21T04:19:43.115679] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-21T04:19:43.118728] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-21T04:19:43.121744] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-21T04:19:43.134510] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-21T04:19:43.140033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1054}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.358081] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-21T04:19:43.360501] [PerformanceMonitoringService] Slow frame detected {"duration_ms":214}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.382778] [OfflineModeService] Offline content sync completed
flutter: ❌ ERROR [2025-07-21T04:19:43.385765] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-21T04:19:43.399398] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-21T04:19:43.411757] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-21T04:19:43.418962] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.448547] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.508383] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.529645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.728815] [PerformanceMonitoringService] Slow frame detected {"duration_ms":199}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.923332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":111}
flutter: 🐛 DEBUG [2025-07-21T04:19:43.945558] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.062040] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.113762] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.161496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.228422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.376553] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-21T04:19:44.412181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.478548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.529578] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.630580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.748223] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.783880] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.831933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.863760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":31}
flutter: 🐛 DEBUG [2025-07-21T04:19:44.968634] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.034834] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.113281] [PerformanceMonitoringService] Slow frame detected {"duration_ms":76}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.163291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.213249] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.278917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.328914] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-21T04:19:45.612963] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.955240] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-21T04:19:45.978590] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: ❌ ERROR [2025-07-21T04:19:46.155609] [FlutterError] A RenderFlex overflowed by 8.9 pixels on the right. A RenderFlex overflowed by 8.9 pixels on the right.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #10     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #11     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #12     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #13     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #17     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #18     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     PaintingContext.pushClipRRect.<anonymous closure> (package:flutter/src/rendering/object.dart:599:83)
flutter: #23     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #24     ClipContext.clipRRectAndPaint (package:flutter/src/painting/clip.dart:48:5)
flutter: #25     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:599:7)
flutter: #26     RenderClipRRect.paint (package:flutter/src/rendering/proxy_box.dart:1676:25)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #34     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #35     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #36     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #51     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #52     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #60     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #61     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #63     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #64     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #65     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #66     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #67     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #68     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #69     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #70     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #71     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #72     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #77     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #78     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #79     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #81     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #93     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #94     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #95     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #97     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #101    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #105    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #106    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #107    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #110    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #111    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #135    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #140    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #141    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #142    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #143    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #144    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #145    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #149    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #150    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #151    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #152    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #156    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #163    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #164    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #165    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #166    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #167    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #171    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #175    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #180    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #181    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #182    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #188    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #189    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #190    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #191    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #192    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #193    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #196    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #197    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #198    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #199    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #200    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #201    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #202    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #203    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #204    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #205    _invoke (dart:ui/hooks.dart:312:13)
flutter: #206    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #207    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-21T04:19:46.200434] [FlutterError] A RenderFlex overflowed by 9.7 pixels on the right. A RenderFlex overflowed by 9.7 pixels on the right.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #10     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #11     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #12     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #13     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #17     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #18     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     PaintingContext.pushClipRRect.<anonymous closure> (package:flutter/src/rendering/object.dart:599:83)
flutter: #23     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #24     ClipContext.clipRRectAndPaint (package:flutter/src/painting/clip.dart:48:5)
flutter: #25     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:599:7)
flutter: #26     RenderClipRRect.paint (package:flutter/src/rendering/proxy_box.dart:1676:25)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #34     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #35     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #36     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #51     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #52     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #60     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #61     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #63     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #64     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #65     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #66     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #67     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #68     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #69     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #70     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #71     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #72     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #77     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #78     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #79     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #81     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #93     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #94     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #95     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #97     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #101    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #105    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #106    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #107    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #110    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #111    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #135    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #140    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #141    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #142    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #143    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #144    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #145    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #149    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #150    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #151    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #152    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #156    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #163    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #164    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #165    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #166    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #167    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #168    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #169    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #170    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #171    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #175    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #180    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #181    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #182    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #188    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #189    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #190    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #191    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #192    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #193    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #196    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #197    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #198    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #199    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #200    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #201    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #202    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #203    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #204    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #205    _invoke (dart:ui/hooks.dart:312:13)
flutter: #206    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #207    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-21T04:19:46.218076] [PerformanceMonitoringService] Slow frame detected {"duration_ms":123}
flutter: 🐛 DEBUG [2025-07-21T04:19:46.278737] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-21T04:19:46.379525] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:46.412302] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-21T04:19:46.629286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-21T04:19:46.945544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.012226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.062998] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.095474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.178348] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.362171] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-07-21T04:19:47.545025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.061644] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.119050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.179823] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.229463] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.313356] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.464423] [PerformanceMonitoringService] Slow frame detected {"duration_ms":152}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.529026] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.561874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:48.695886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.295322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.345126] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.428471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.462772] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.536072] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.578753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.629039] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.678667] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.729028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.779090] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.862487] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.912496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:49.979454] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.029680] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.112176] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.162173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.245756] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.312241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.379257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.449289] [PerformanceMonitoringService] Slow frame detected {"duration_ms":70}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.495296] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.561969] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.628455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.694905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.745792] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.811665] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.879417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.928668] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-21T04:19:50.996636] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.062406] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.129232] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.195712] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.263549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.312304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.362403] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.428739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.495791] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.562235] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.611909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.679381] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-21T04:19:51.728785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-21T04:19:53.383737] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-21T04:19:59.363932] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: ⚠️ WARNING [2025-07-21T04:20:08.383269] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: ⚠️ WARNING [2025-07-21T04:20:23.382628] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
