import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/ar_launch_screen.dart';

/// AR Guide Screen - Main AR Guide interface
/// Combines IMG_AR1.PNG and IMG_AR2.PNG designs as a comprehensive main screen
class ARGuideScreen extends ConsumerStatefulWidget {
  const ARGuideScreen({super.key});

  @override
  ConsumerState<ARGuideScreen> createState() => _ARGuideScreenState();
}

class _ARGuideScreenState extends ConsumerState<ARGuideScreen> {
  @override
  void initState() {
    super.initState();
    // Set status bar for normal screen experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header Section
              _buildHeader(context),

              // IMG_AR1 Section (Upper section)
              _buildUpperSection(context),

              // IMG_AR2 Section (Lower section)
              _buildLowerSection(context),

              // AR Learning Hub Section (IMG_AR3.PNG content)
              _buildARLearningHub(context),

              // Explore Nearby Attractions Section (IMG_AR4.PNG content)
              _buildExploreNearbyAttractions(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the header section
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 20,
            ),
          ),
          // Title
          const Expanded(
            child: Text(
              'AR Guide',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                fontFamily: AppTheme.fontFamily,
              ),
            ),
          ),
          // Spacer to balance the back button
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  /// Build the upper section (Hero AR Card - IMG_AR1.PNG content)
  Widget _buildUpperSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMd),
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Stack(
        children: [
          // Map background
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  AppTheme.secondaryColor.withValues(alpha: 0.5),
                ],
              ),
            ),
          ),

          // Badges positioned at top
          Positioned(
            top: AppTheme.spacingMd,
            left: AppTheme.spacingMd,
            child: _buildLiveARBadge(),
          ),
          Positioned(
            top: AppTheme.spacingMd,
            right: AppTheme.spacingMd,
            child: _buildLocationBadge(),
          ),

          // Center content
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Start Your AR Journey',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingSm),
                const Text(
                  'Point your camera and discover hidden stories',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingLg),

                // Launch AR Camera Button
                ElevatedButton.icon(
                  onPressed: () => _launchARCamera(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingLg,
                      vertical: AppTheme.spacingMd,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusMedium),
                    ),
                    elevation: 0,
                  ),
                  icon: const Icon(Icons.camera_alt, size: 20),
                  label: const Text(
                    'Launch AR Camera',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the lower section (AR Capabilities Grid - IMG_AR1.PNG content)
  Widget _buildLowerSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          const Text(
            'AR Capabilities',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),

          // AR Capabilities Grid (2x3)
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: AppTheme.spacingMd,
            mainAxisSpacing: AppTheme.spacingMd,
            childAspectRatio: 1.1,
            children: [
              _buildCapabilityCard(
                icon: Icons.translate,
                title: 'Live Translation',
                description: 'Real-time text translation',
                color: AppTheme.primaryColor,
              ),
              _buildCapabilityCard(
                icon: Icons.visibility,
                title: 'Object Recognition',
                description: 'Identify landmarks instantly',
                color: AppTheme.secondaryColor,
              ),
              _buildCapabilityCard(
                icon: Icons.navigation,
                title: 'Navigation',
                description: 'AR-powered directions',
                color: AppTheme.accentColor,
              ),
              _buildCapabilityCard(
                icon: Icons.camera_enhance,
                title: 'Smart Camera',
                description: 'Enhanced photo capture',
                color: Colors.purple,
              ),
              _buildCapabilityCard(
                icon: Icons.headphones,
                title: 'Audio Guide',
                description: 'Immersive storytelling',
                color: Colors.orange,
              ),
              _buildCapabilityCard(
                icon: Icons.offline_bolt,
                title: 'Offline Mode',
                description: 'Works without internet',
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build AR Capability Card for grid layout
  Widget _buildCapabilityCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppTheme.spacingXs),
          Flexible(
            child: Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Live AR Badge
  Widget _buildLiveARBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: const Text(
        'Live AR',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Build Location Badge
  Widget _buildLocationBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            size: 14,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: AppTheme.spacingXs),
          const Text(
            'Downtown',
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build AR Learning Hub Section (IMG_AR3.PNG content)
  Widget _buildARLearningHub(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppTheme.spacingLg),

          // Section title
          const Text(
            'AR Learning Hub',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),

          // Learning modules
          _buildLearningModule(
            icon: Icons.play_circle_outline,
            title: 'AR Basics Tutorial',
            description: 'Learn the fundamentals of AR navigation',
            progress: 0.8,
            isCompleted: false,
          ),
          const SizedBox(height: AppTheme.spacingMd),

          _buildLearningModule(
            icon: Icons.camera_alt,
            title: 'Camera Controls',
            description: 'Master AR camera features and settings',
            progress: 1.0,
            isCompleted: true,
          ),
          const SizedBox(height: AppTheme.spacingMd),

          _buildLearningModule(
            icon: Icons.location_on,
            title: 'Location-Based AR',
            description: 'Discover places with location AR features',
            progress: 0.3,
            isCompleted: false,
          ),
          const SizedBox(height: AppTheme.spacingMd),

          _buildLearningModule(
            icon: Icons.translate,
            title: 'AR Translation',
            description: 'Use real-time translation in AR mode',
            progress: 0.0,
            isCompleted: false,
          ),
          const SizedBox(height: AppTheme.spacingMd),

          _buildLearningModule(
            icon: Icons.info_outline,
            title: 'Cultural Insights',
            description: 'Access rich cultural information through AR',
            progress: 0.6,
            isCompleted: false,
          ),
        ],
      ),
    );
  }

  /// Launch AR Camera - Navigate to intermediate AR Launch Screen
  void _launchARCamera(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARLaunchScreen(),
      ),
    );
  }

  /// Build Learning Module Card with Tap Interactions
  Widget _buildLearningModule({
    required IconData icon,
    required String title,
    required String description,
    required double progress,
    required bool isCompleted,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _handleLearningModuleTap(title, isCompleted, progress),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            boxShadow: AppTheme.shadowMedium,
            border: Border.all(
              color: isCompleted ? AppTheme.primaryColor : Colors.grey.shade200,
              width: isCompleted ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Icon container
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppTheme.primaryColor
                      : AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
                child: Icon(
                  isCompleted ? Icons.check_circle : icon,
                  color: isCompleted ? Colors.white : AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingXs),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),

                    // Progress bar
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: progress,
                        child: Container(
                          decoration: BoxDecoration(
                            color: isCompleted
                                ? AppTheme.primaryColor
                                : AppTheme.accentColor,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Status indicator
              if (isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingSm,
                    vertical: AppTheme.spacingXs,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: const Text(
                    'Completed',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                )
              else
                Text(
                  '${(progress * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build Explore Nearby Attractions Section (IMG_AR4.PNG content)
  Widget _buildExploreNearbyAttractions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppTheme.spacingLg),

          // Section title
          const Text(
            'Explore Nearby Attractions',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),

          // Horizontal scrolling attraction cards
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _nearbyAttractions.length,
              itemBuilder: (context, index) {
                final attraction = _nearbyAttractions[index];
                return Container(
                  width: 220,
                  margin: EdgeInsets.only(
                    right: index < _nearbyAttractions.length - 1
                        ? AppTheme.spacingMd
                        : 0,
                  ),
                  child: _buildAttractionCard(attraction),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Sample nearby attractions data
  List<Map<String, dynamic>> get _nearbyAttractions => [
        {
          'name': 'Historic Cathedral',
          'description': 'Beautiful 12th century architecture',
          'rating': 4.8,
          'distance': '0.2 km',
          'image': 'assets/images/cathedral.jpg',
        },
        {
          'name': 'Art Museum',
          'description': 'Contemporary and classical art collection',
          'rating': 4.6,
          'distance': '0.5 km',
          'image': 'assets/images/museum.jpg',
        },
        {
          'name': 'Cultural Square',
          'description': 'Historic town center with local vendors',
          'rating': 4.7,
          'distance': '0.3 km',
          'image': 'assets/images/square.jpg',
        },
      ];

  /// Build Attraction Card for horizontal scrolling
  Widget _buildAttractionCard(Map<String, dynamic> attraction) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Attraction image
          Container(
            height: 140,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                topRight: Radius.circular(AppTheme.borderRadiusLarge),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  AppTheme.secondaryColor.withValues(alpha: 0.5),
                ],
              ),
            ),
            child: const Center(
              child: Icon(
                Icons.photo_camera,
                size: 40,
                color: Colors.white,
              ),
            ),
          ),

          // Card content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Attraction name
                  Text(
                    attraction['name'] ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppTheme.spacingXs),

                  // Description
                  Flexible(
                    child: Text(
                      attraction['description'] ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingSm),

                  // Rating and distance
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: AppTheme.spacingXs),
                      Text(
                        '${attraction['rating'] ?? 0.0}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        attraction['distance'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.spacingMd),

                  // Start AR button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () =>
                          _startARForAttraction(attraction['name'] ?? ''),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            vertical: AppTheme.spacingSm),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusSmall),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Start AR',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle Learning Module Tap - Navigate based on completion status
  void _handleLearningModuleTap(
      String title, bool isCompleted, double progress) {
    // Provide haptic feedback
    HapticFeedback.lightImpact();

    String message;
    if (isCompleted) {
      message = 'Opening $title review...';
      // TODO: Navigate to review/summary screen
      // Navigator.push(context, MaterialPageRoute(builder: (context) => ReviewScreen(title: title)));
    } else if (progress > 0) {
      message = 'Continuing $title (${(progress * 100).toInt()}% complete)...';
      // TODO: Navigate to continue learning screen
      // Navigator.push(context, MaterialPageRoute(builder: (context) => ContinueLearningScreen(title: title, progress: progress)));
    } else {
      message = 'Starting $title...';
      // TODO: Navigate to introduction/start screen
      // Navigator.push(context, MaterialPageRoute(builder: (context) => StartLearningScreen(title: title)));
    }

    // Show feedback message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        ),
      ),
    );
  }

  /// Start AR for specific attraction
  void _startARForAttraction(String attractionName) {
    // Provide haptic feedback
    HapticFeedback.lightImpact();

    // Show feedback and navigate to AR experience
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting AR experience for $attractionName...'),
        backgroundColor: AppTheme.primaryColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        ),
      ),
    );

    // Navigate to AR Launch Screen with attraction context
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARLaunchScreen(),
      ),
    );
  }

  /// Show tutorial
  void _showTutorial(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR Tutorial coming soon!'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
