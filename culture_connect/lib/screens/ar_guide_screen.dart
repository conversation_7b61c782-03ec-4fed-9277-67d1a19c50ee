import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/ar_launch_screen.dart';

/// AR Guide Screen - Main AR Guide interface
/// Combines IMG_AR1.PNG and IMG_AR2.PNG designs as a comprehensive main screen
class ARGuideScreen extends ConsumerStatefulWidget {
  const ARGuideScreen({super.key});

  @override
  ConsumerState<ARGuideScreen> createState() => _ARGuideScreenState();
}

class _ARGuideScreenState extends ConsumerState<ARGuideScreen> {
  @override
  void initState() {
    super.initState();
    // Set status bar for normal screen experience
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header Section
              _buildHeader(context),

              // IMG_AR1 Section (Upper section)
              _buildUpperSection(context),

              // IMG_AR2 Section (Lower section)
              _buildLowerSection(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the header section
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 20,
            ),
          ),
          // Title
          const Expanded(
            child: Text(
              'AR Guide',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                fontFamily: AppTheme.fontFamily,
              ),
            ),
          ),
          // Spacer to balance the back button
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  /// Build the upper section (Hero AR Card - IMG_AR1.PNG content)
  Widget _buildUpperSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMd),
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Stack(
        children: [
          // Map background
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  AppTheme.secondaryColor.withValues(alpha: 0.5),
                ],
              ),
            ),
          ),

          // Badges positioned at top
          Positioned(
            top: AppTheme.spacingMd,
            left: AppTheme.spacingMd,
            child: _buildLiveARBadge(),
          ),
          Positioned(
            top: AppTheme.spacingMd,
            right: AppTheme.spacingMd,
            child: _buildLocationBadge(),
          ),

          // Center content
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Start Your AR Journey',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingSm),
                const Text(
                  'Point your camera and discover hidden stories',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingLg),

                // Launch AR Camera Button
                ElevatedButton.icon(
                  onPressed: () => _launchARCamera(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingLg,
                      vertical: AppTheme.spacingMd,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusMedium),
                    ),
                    elevation: 0,
                  ),
                  icon: const Icon(Icons.camera_alt, size: 20),
                  label: const Text(
                    'Launch AR Camera',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the lower section (AR Capabilities Grid - IMG_AR1.PNG content)
  Widget _buildLowerSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          const Text(
            'AR Capabilities',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: AppTheme.spacingLg),

          // AR Capabilities Grid (2x3)
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: AppTheme.spacingMd,
            mainAxisSpacing: AppTheme.spacingMd,
            childAspectRatio: 1.1,
            children: [
              _buildCapabilityCard(
                icon: Icons.translate,
                title: 'Live Translation',
                description: 'Real-time text translation',
                color: AppTheme.primaryColor,
              ),
              _buildCapabilityCard(
                icon: Icons.visibility,
                title: 'Object Recognition',
                description: 'Identify landmarks instantly',
                color: AppTheme.secondaryColor,
              ),
              _buildCapabilityCard(
                icon: Icons.navigation,
                title: 'Navigation',
                description: 'AR-powered directions',
                color: AppTheme.accentColor,
              ),
              _buildCapabilityCard(
                icon: Icons.camera_enhance,
                title: 'Smart Camera',
                description: 'Enhanced photo capture',
                color: Colors.purple,
              ),
              _buildCapabilityCard(
                icon: Icons.headphones,
                title: 'Audio Guide',
                description: 'Immersive storytelling',
                color: Colors.orange,
              ),
              _buildCapabilityCard(
                icon: Icons.offline_bolt,
                title: 'Offline Mode',
                description: 'Works without internet',
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build AR Capability Card for grid layout
  Widget _buildCapabilityCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppTheme.spacingXs),
          Flexible(
            child: Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Live AR Badge
  Widget _buildLiveARBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: const Text(
        'Live AR',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Build Location Badge
  Widget _buildLocationBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMd,
        vertical: AppTheme.spacingSm,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            size: 14,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: AppTheme.spacingXs),
          const Text(
            'Downtown',
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Launch AR Camera - Navigate to intermediate AR Launch Screen
  void _launchARCamera(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARLaunchScreen(),
      ),
    );
  }

  /// Show tutorial
  void _showTutorial(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR Tutorial coming soon!'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
